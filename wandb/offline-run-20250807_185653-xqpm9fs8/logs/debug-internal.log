{"time":"2025-08-07T18:56:53.381893197+03:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-07T18:56:53.486972062+03:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-07T18:56:53.487024123+03:00","level":"INFO","msg":"stream: created new stream","id":"xqpm9fs8"}
{"time":"2025-08-07T18:56:53.487033791+03:00","level":"INFO","msg":"stream: started","id":"xqpm9fs8"}
{"time":"2025-08-07T18:56:53.487044903+03:00","level":"INFO","msg":"writer: Do: started","stream_id":"xqpm9fs8"}
{"time":"2025-08-07T18:56:53.487055543+03:00","level":"INFO","msg":"sender: started","stream_id":"xqpm9fs8"}
{"time":"2025-08-07T18:56:53.48705929+03:00","level":"INFO","msg":"handler: started","stream_id":"xqpm9fs8"}
{"time":"2025-08-07T18:56:53.487239799+03:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-07T19:04:50.019741061+03:00","level":"INFO","msg":"stream: closing","id":"xqpm9fs8"}
{"time":"2025-08-07T19:04:50.019823899+03:00","level":"INFO","msg":"handler: closed","stream_id":"xqpm9fs8"}
{"time":"2025-08-07T19:04:50.019829079+03:00","level":"INFO","msg":"writer: Close: closed","stream_id":"xqpm9fs8"}
{"time":"2025-08-07T19:04:50.019857453+03:00","level":"INFO","msg":"sender: closed","stream_id":"xqpm9fs8"}
{"time":"2025-08-07T19:04:50.019895766+03:00","level":"INFO","msg":"stream: closed","id":"xqpm9fs8"}
