{"time":"2025-08-07T20:29:56.678227757+03:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-07T20:29:56.783703477+03:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-07T20:29:56.783980079+03:00","level":"INFO","msg":"stream: created new stream","id":"lhp1pzs7"}
{"time":"2025-08-07T20:29:56.783988806+03:00","level":"INFO","msg":"stream: started","id":"lhp1pzs7"}
{"time":"2025-08-07T20:29:56.784007522+03:00","level":"INFO","msg":"writer: Do: started","stream_id":"lhp1pzs7"}
{"time":"2025-08-07T20:29:56.78401682+03:00","level":"INFO","msg":"sender: started","stream_id":"lhp1pzs7"}
{"time":"2025-08-07T20:29:56.784034724+03:00","level":"INFO","msg":"handler: started","stream_id":"lhp1pzs7"}
{"time":"2025-08-07T20:29:56.784226673+03:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-07T20:29:57.787602333+03:00","level":"INFO","msg":"stream: closing","id":"lhp1pzs7"}
{"time":"2025-08-07T20:29:57.789039697+03:00","level":"INFO","msg":"handler: closed","stream_id":"lhp1pzs7"}
{"time":"2025-08-07T20:29:57.789049756+03:00","level":"INFO","msg":"writer: Close: closed","stream_id":"lhp1pzs7"}
{"time":"2025-08-07T20:29:57.78905168+03:00","level":"INFO","msg":"sender: closed","stream_id":"lhp1pzs7"}
{"time":"2025-08-07T20:29:57.789083481+03:00","level":"INFO","msg":"stream: closed","id":"lhp1pzs7"}
