{"time":"2025-08-07T20:28:31.883240683+03:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-07T20:28:31.987862201+03:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-07T20:28:31.987917538+03:00","level":"INFO","msg":"stream: created new stream","id":"vtc23h8m"}
{"time":"2025-08-07T20:28:31.987927738+03:00","level":"INFO","msg":"stream: started","id":"vtc23h8m"}
{"time":"2025-08-07T20:28:31.987950542+03:00","level":"INFO","msg":"writer: Do: started","stream_id":"vtc23h8m"}
{"time":"2025-08-07T20:28:31.987956523+03:00","level":"INFO","msg":"handler: started","stream_id":"vtc23h8m"}
{"time":"2025-08-07T20:28:31.987961633+03:00","level":"INFO","msg":"sender: started","stream_id":"vtc23h8m"}
{"time":"2025-08-07T20:28:31.988155988+03:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-07T20:28:32.991170177+03:00","level":"INFO","msg":"stream: closing","id":"vtc23h8m"}
{"time":"2025-08-07T20:28:32.991250953+03:00","level":"INFO","msg":"handler: closed","stream_id":"vtc23h8m"}
{"time":"2025-08-07T20:28:32.991260782+03:00","level":"INFO","msg":"writer: Close: closed","stream_id":"vtc23h8m"}
{"time":"2025-08-07T20:28:32.991274929+03:00","level":"INFO","msg":"sender: closed","stream_id":"vtc23h8m"}
{"time":"2025-08-07T20:28:32.991308995+03:00","level":"INFO","msg":"stream: closed","id":"vtc23h8m"}
