pure_eval==0.2.3
pydantic==2.11.7
multidict==6.6.3
requests==2.32.4
packaging==25.0
nvidia-cuda-cupti-cu12==12.6.80
magicattr==0.1.6
sympy==1.14.0
flashinfer-python==0.2.7.post1
torch_memory_saver==0.0.8
nvidia-curand-cu12==*********
scipy==1.16.1
psutil==7.0.0
nvidia-cufile-cu12==********
peft==0.17.0
pexpect==4.9.0
importlib_metadata==8.7.0
pycountry==24.6.1
cuda-python==13.0.0
pycryptodomex==3.23.0
frozenlist==1.7.0
smmap==5.0.2
huggingface-hub==0.34.3
interegular==0.3.3
markdown-it-py==3.0.0
six==1.17.0
jsonschema-specifications==2025.4.1
rpds-py==0.27.0
jedi==0.19.2
decord==0.6.0
cuda-bindings==13.0.0
gitdb==4.0.12
nvidia-ml-py==12.575.51
cffi==1.17.1
hf-xet==1.1.7
cloudpickle==3.1.1
h11==0.16.0
sgl-kernel==0.2.6.post1
Pygments==2.19.2
platformdirs==4.3.8
torchvision==0.22.1
nvidia-cuda-runtime-cu12==12.6.77
aiosignal==1.4.0
ninja==********
Mako==1.3.10
transformers==4.53.2
tqdm==4.67.1
Jinja2==3.1.6
xgrammar==0.1.21
backoff==2.2.1
triton==3.3.1
torchaudio==2.7.1
idna==3.10
nvidia-cublas-cu12==********
tenacity==9.1.2
pyzmq==27.0.1
nvidia-cusparselt-cu12==0.6.3
soundfile==0.13.1
nvidia-nvtx-cu12==12.6.77
setuptools==80.9.0
python-dateutil==2.9.0.post0
jiter==0.10.0
propcache==0.3.2
nvidia-nvshmem-cu12==3.3.20
torch==2.7.1
pillow==11.3.0
einops==0.8.1
filelock==3.18.0
nvidia-cudnn-cu12==********
outlines==0.1.11
sniffio==1.3.1
aiohappyeyeballs==2.6.1
trl==0.20.0
networkx==3.5
pyarrow==21.0.0
pydantic_core==2.33.2
pandas==2.3.1
protobuf==6.31.1
SQLAlchemy==2.0.42
modelscope==1.28.2
asttokens==3.0.0
parso==0.8.4
cuda-pathfinder==1.1.0
sentry-sdk==2.34.1
pyproject_hooks==1.2.0
fsspec==2025.3.0
wandb==0.21.0
aiohttp==3.12.15
numpy==2.3.2
attrs==25.3.0
sglang==0.4.9.post3
pynvml==12.0.0
pybase64==1.4.2
nvidia-cusolver-cu12==********
prompt_toolkit==3.0.51
openai==1.99.1
ipython==9.4.0
MarkupSafe==3.0.2
asyncer==0.0.8
lxml==6.0.0
safetensors==0.6.1
fastapi==0.116.1
hf_transfer==0.1.9
pycparser==2.22
nvidia-cufft-cu12==********
matplotlib-inline==0.1.7
ujson==5.10.0
uvloop==0.21.0
zipp==3.23.0
accelerate==1.10.0
multiprocess==0.70.16
build==1.3.0
ipython_pygments_lexers==1.1.1
dill==0.3.8
httpcore==1.0.9
blobfile==3.0.0
GitPython==3.1.45
alembic==1.16.4
httpx==0.28.1
lark==1.2.2
certifi==2025.8.3
msgspec==0.19.0
optuna==4.4.0
diskcache==5.6.3
nvidia-nccl-cu12==2.26.2
torchao==0.9.0
rich==14.1.0
sentencepiece==0.2.1
partial-json-parser==*******.post6
json_repair==0.48.0
litellm==1.75.0
starlette==0.47.2
tiktoken==0.10.0
click==8.2.1
typing_extensions==4.14.1
nvidia-cuda-nvrtc-cu12==12.6.77
prometheus_client==0.22.1
xxhash==3.5.0
mpmath==1.3.0
referencing==0.36.2
stack-data==0.6.3
nvidia-nvjitlink-cu12==12.6.85
PyYAML==6.0.2
orjson==3.11.1
executing==2.2.0
outlines_core==0.1.26
setproctitle==1.3.6
wcwidth==0.2.13
mdurl==0.1.2
uvicorn==0.35.0
tzdata==2025.2
regex==2025.7.34
yarl==1.20.1
urllib3==2.5.0
ptyprocess==0.7.0
charset-normalizer==3.4.2
nest-asyncio==1.6.0
python-multipart==0.0.20
distro==1.9.0
jsonschema==4.25.0
python-dotenv==1.1.1
anthropic==0.61.0
traitlets==5.14.3
llguidance==0.7.30
joblib==1.5.1
compressed-tensors==0.10.2
greenlet==3.2.4
nvidia-cusparse-cu12==********
airportsdata==20250706
tokenizers==0.21.4
pytz==2025.2
timm==1.0.16
datasets==4.0.0
dspy==2.6.27
decorator==5.2.1
annotated-types==0.7.0
typing-inspection==0.4.1
colorlog==6.9.0
cachetools==6.1.0
anyio==4.10.0
